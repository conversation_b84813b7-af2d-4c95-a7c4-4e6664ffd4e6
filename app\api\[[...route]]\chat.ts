import { <PERSON><PERSON> } from "hono";
import {
  HumanMessage,
  SystemMessage,
  BaseMessage,
  AIMessage,
} from "@langchain/core/messages";
import { StringOutputParser } from "@langchain/core/output_parsers";
import { Annotation } from "@langchain/langgraph";
import { sp_TV } from "@/lib/prompt";
import { tool } from "@langchain/core/tools";
import { ChatOpenAI } from "@langchain/openai";
import { ChatTogetherAI } from "@langchain/community/chat_models/togetherai";
import { Agent } from "@/lib/ai-functions";
import { z } from "zod";
import {
  schemaThemes,
  vocabularyResponseSchema,
  correctionResponseSchema,
} from "@/lib/schemas";
import { ChatMistralAI } from "@langchain/mistralai";
import { correctionSchema } from "@/lib/schemas";
import { flexibleTranslationSchema } from "@/lib/schemas";
import { Message } from "@/hooks/use-manage-chat";
import { XMLParser } from "fast-xml-parser";
import { PrismaClient } from '@prisma/client';
import { chatSectionSchema } from '@/lib/schemas';

const searchtool = tool(
  (input) => {
    return "search result";
  },
  {
    name: "searchtool",
    description: "search tool",
    schema: z.string().describe("search to be made"),
  }
);
// Helper function for weighted random selection
function selectWeightedRandom<T>(items: { value: T; weight: number }[]): T | null {
  if (items.length === 0) {
    return null;
  }

  const totalWeight = items.reduce((sum, item) => sum + item.weight, 0);
  if (totalWeight === 0) {
    return items[Math.floor(Math.random() * items.length)].value;
  }

  let random = Math.random() * totalWeight;

  for (const item of items) {
    if (random < item.weight) {
      return item.value;
    }
    random -= item.weight;
  }

  return items[items.length - 1].value;
}

// Helper function for selecting multiple weighted random items
function selectMultipleWeightedRandom<T>(
  items: { value: T; weight: number }[],
  count: number
): T[] {
  if (items.length === 0 || count <= 0) {
    return [];
  }

  // If we want more items than available, return all items
  if (count >= items.length) {
    return items.map(item => item.value);
  }

  const selected: T[] = [];
  const availableItems = [...items]; // Create a copy to avoid mutating original

  for (let i = 0; i < count; i++) {
    const selectedItem = selectWeightedRandom(availableItems);
    if (selectedItem !== null) {
      selected.push(selectedItem);
      // Remove the selected item from available items to avoid duplicates
      const index = availableItems.findIndex(item => item.value === selectedItem);
      if (index > -1) {
        availableItems.splice(index, 1);
      }
    }
  }

  return selected;
}


const DEFAULT_GRAMMAR_RULE_WEIGHT = 5;


async function retry<T>(fn: () => Promise<T>, retries = 3): Promise<T> {
  let attempts = 0;
  while (attempts < retries) {
    try {
      return await fn();
    } catch (error) {
      attempts++;
      if (attempts >= retries) throw error; // Rethrow if all retries fail
    }
  }
  throw new Error("Unexpected error"); // Fallback error
}

const app = new Hono()
  .post("/", async (c) => {
    const openai = new ChatOpenAI({
      model: "gpt-4o",
    }); /* .withStructuredOutput(schemaThemes) */

    const togetherai = new ChatTogetherAI({
      model: "deepseek-ai/DeepSeek-V3",
      temperature: 0,
    }); /* .withStructuredOutput(schemaThemes) */

    const mistralai = new ChatMistralAI({
      model: "mistral-large-latest",
      temperature: 0,
    }); /* .withStructuredOutput(schemaThemes) */

    const openaiC = new ChatOpenAI({
      model: "gpt-4o",
    }); /* .withStructuredOutput(correctionSchema) */

    const togetheraiC = new ChatTogetherAI({
      model: "deepseek-ai/DeepSeek-V3",
      temperature: 0,
    }); /* .withStructuredOutput(schemaThemes) */

    const litellmDeepSeek = new ChatOpenAI({
      model: "deepseek/deepseek-chat-v3-0324:free",
      configuration: {
        basePath: "https://litellm.tadzlab.xyz",
      },
      apiKey: process.env.LITELLM_API_KEY,
    });

    const litellmDeepSeekC = new ChatOpenAI({
      model: "deepseek/deepseek-chat-v3-0324:free",
      configuration: {
        basePath: "https://litellm.tadzlab.xyz",
      },
      apiKey: process.env.LITELLM_API_KEY,
    }); /* .withStructuredOutput(correctionSchema) */

    const mistralaiC = new ChatMistralAI({
      model: "mistral-large-latest",
      temperature: 0,
    }); /* .withStructuredOutput(schemaThemes) */

    // Parse the JSON body
    const { input, messages, selectedTopics, selectedGrammarRules, chatSectionId, grammarRuleCount } =
      await c.req.json<{
        input: string;
        messages: Message[];
        selectedTopics: string[];
        selectedGrammarRules: { value: string; weight: number }[];
        chatSectionId?: string; // Optional chat section ID for saving
        grammarRuleCount?: number; // Number of grammar rules to select
      }>();
    console.log("Received input:", messages);
    const prisma = new PrismaClient();

    // Select multiple grammar rules based on weights and count
    const defaultGrammarRuleCount = 1; // Default to 1 if not specified
    const rulesToSelect = grammarRuleCount || defaultGrammarRuleCount;
    let chosenGrammarRules: string[] = [];

    if (selectedGrammarRules && selectedGrammarRules.length > 0) {
      chosenGrammarRules = selectMultipleWeightedRandom(selectedGrammarRules, rulesToSelect);
    }

    console.log("Chosen grammar rules:", chosenGrammarRules);
  

    // For backward compatibility, keep the single rule variable
    const chosenGrammarRule = chosenGrammarRules.length > 0 ? chosenGrammarRules : null;

    const newMesssages = messages.map((message) => {
      if (message.role === "user") {
        return new HumanMessage(message.content);
      } else {
        return new AIMessage(message.content);
      }
    });

    /* 
        
                const AgentState = Annotation.Root({
          messages: Annotation<BaseMessage[]>({
            reducer: (x, y) => x.concat(y),
            default: () => {
              let systemMessageContent = `Your are my english teacher. you help me learn english by disussing with me.`;
              if (selectedTopics && selectedTopics.length > 0) {
                systemMessageContent += ` The current topics we discuss about are: ${selectedTopics.join(", ")}. suggest me what could be the ideas of my next response `;
              }
              if (selectedGrammarRules && selectedGrammarRules.length > 0) {
                systemMessageContent += `for each of your response you will constraint me to use some grammatical rules that you will specify. 
                In your response contained in the <message></message>, correct my privious response, if the way i used you recommended grammar rules was uncorrect.
                 Focus on exercising the following grammar rules: ${selectedGrammarRules.join(", ")}.`;
              }
              return [
                new SystemMessage(systemMessageContent),
                ...newMesssages
              ];
            }
          }),
        });

        */

    const AgentState = Annotation.Root({
      messages: Annotation<BaseMessage[]>({
        reducer: (x, y) => x.concat(y),
        default: () => {
          let systemMessageContent = `Your are my english teacher. you help me learn english by disussing with me. you primary task is to ensure the discussions flows.
Your response MUST be formatted as XML according to this structure:
<response>
  <suggestion>
    <grammar_rule>${chosenGrammarRule || 'None'}</grammar_rule>
    <next_response_ideas>
      <idea>A creative and relevant idea for the user's next message.</idea>
      <idea>Another creative and relevant idea.</idea>
      ...
    </next_response_ideas>
  </suggestion>
  <feedback>Comments about the user's previous message, including any grammar corrections, vocabulary suggestions, or language learning observations.</feedback>
  <message>Your conversational response to the user's message goes here. Continue the discussion naturally.</message>
</response>
`;
          if (selectedTopics && selectedTopics.length > 0) {
            systemMessageContent += `\nThe current topics we discuss about are: ${selectedTopics.join(
              ", "
            )}. feel free to suggest questions connecting one another or be specific to only one of the topics.`;
          }
          if (chosenGrammarRules && chosenGrammarRules.length > 0) {      
            systemMessageContent += `\nfor each of your response you will be provided with a list of grammatical rules i should use in my next response. Grammar rules i must use in my next response:  ${chosenGrammarRules.join(", ")}. always force me two use all of them !!! always list all of them in the grammar rule section. if you want me to enphasize in a particular one just ad an emoji in front of it without remmoving other grammar rules.`;      
          } 
          systemMessageContent += `  feel free to propose outside the scope of this list. and even be specific ( which exact tenses should i use, which conditional,...). Your are allow to make use of any grammar rule between A2-B2  (also some C1 grammar rules )"`;
          return [new SystemMessage(systemMessageContent), ...newMesssages];
        },
      }),
    });

    const AgentStateCorrect = Annotation.Root({
      messages: Annotation<BaseMessage[]>({
        reducer: (x, y) => x.concat(y),
        default: () => [
          new SystemMessage(
            `You help improve my English by correcting errors and responding to queries.
                 Your response MUST be formatted as XML according to this structure:
                 <correction>
                   <possibilities>
                     <possibility>alternative expression 1</possibility>
                     <possibility>alternative expression 2</possibility>
                   </possibilities>
                   <correctedSentence error="true|false">
                     <sentence>the corrected sentence with *errors* in bold</sentence>
                     <explanation>explanation of the errors made</explanation>
                   </correctedSentence>
                 </correction>`
          ),
        ],
      }),
    });

    const preAgent = new Agent({
      model: litellmDeepSeek,
      State: AgentState,
      tools: [searchtool],
    });
    const agent = preAgent.create();

    const preAgentCorrect = new Agent({
      model: litellmDeepSeekC,
      State: AgentStateCorrect,
      tools: [searchtool],
    });
    const agentCorrect = preAgentCorrect.create();

    // Create a new human message
    const humanMessage = new HumanMessage(input);

    let success = false;
    let attempts = 0;

    while (!success && attempts < 5) {
      try {
        attempts += 1;
        // Invoke the agent with the human message
        const [vocabularyResponse, correctionResponse] = await Promise.all([
          retry(() =>
            agent.invoke(
              { messages: [humanMessage] },
              { configurable: { thread_id: "50" } }
            )
          ),
          retry(() =>
            agentCorrect.invoke(
              { messages: [humanMessage] },
              { configurable: { thread_id: "51" } }
            )
          ),
        ]);

        const parsedVocabularyResponse = await new StringOutputParser().invoke(
          vocabularyResponse.messages[vocabularyResponse.messages.length - 1]
        );

        const parsedCorrectionResponse = await new StringOutputParser().invoke(
          correctionResponse.messages[correctionResponse.messages.length - 1]
        );

        // Parse XML to JSON for correction response
        const correctionParser = new XMLParser({
          ignoreAttributes: false,
          isArray: (name) => name === "possibility",
          attributeNamePrefix: "@_",
        });
        const correctionXmlData = parsedCorrectionResponse;
        const correctionJsonObj = correctionParser.parse(
          `<root>${correctionXmlData}</root>`
        );
        const correctionData = {
          possibilties:
            correctionJsonObj.root.correction.possibilities.possibility || [],
          correctedSentence: correctionJsonObj.root.correction.correctedSentence
            ? {
                error:
                  correctionJsonObj.root.correction.correctedSentence[
                    "@_error"
                  ] === "true",
                sentence:
                  correctionJsonObj.root.correction.correctedSentence.sentence,
                explanation:
                  correctionJsonObj.root.correction.correctedSentence
                    .explanation,
              }
            : null,
        };

        // Parse XML to JSON for vocabulary response
        const vocabParser = new XMLParser({
          ignoreAttributes: false,
          isArray: (name) => name === "idea",
        });
        const vocabJsonObj = vocabParser.parse(parsedVocabularyResponse);

        const vocabularyData = {
          suggestion: {
            grammar_rule: String(vocabJsonObj.response.suggestion.grammar_rule || ''), // Ensure grammar_rule is always a string
            next_response_ideas:
              vocabJsonObj.response.suggestion.next_response_ideas.idea || [],
            feedback: String(vocabJsonObj.response.feedback || ''), // Add feedback field
          },
          message: vocabJsonObj.response.message,
        };

        console.log("Vocabulary response:", vocabularyData);
        console.log("Correction response:", correctionData);

        // Validate the parsed data against the schemas
        const validatedVocabulary = vocabularyResponseSchema.parse(vocabularyData);
        const validatedCorrection = correctionResponseSchema.parse(correctionData);

        success = true;

        // Save chat section and messages to the database
        let currentChatSectionId = chatSectionId;
        if (!currentChatSectionId) {
          // Create a new chat section if one doesn't exist
          const newChatSection = await prisma.chatSection.create({
            data: {
              content: input, // Or a summary of the initial chat
              grammarRuleCount: rulesToSelect, // Save the grammar rule count
              chatSectionTopics: { // Use the linking table for topics
                create: selectedTopics.map(topicValue => ({
                  customChatSetting: { connect: { value: topicValue } },
                })),
              },
              grammarRuleSections: {
                create: selectedGrammarRules.map(rule => ({
                  grammarRule: { connect: { value: rule.value } }, // Connect by unique value
                  weight: rule.weight,
                })),
              },
              messages: {
                create: [
                  ...messages.map(msg => ({
                    role: msg.role,
                    content: msg.content,
                    correction: msg.correction ? JSON.parse(JSON.stringify(msg.correction)) : undefined, // Save correction
                    suggestion: msg.suggestion ? JSON.parse(JSON.stringify(msg.suggestion)) : undefined, // Save suggestion
                  })),
                  {
                    role: 'user',
                    content: input,
                    correction: correctionData ? JSON.parse(JSON.stringify(correctionData)) : undefined, // Save correction to user message
                  },
                  {
                    role: 'assistant',
                    content: vocabularyData.message,
                    suggestion: vocabularyData.suggestion ? JSON.parse(JSON.stringify(vocabularyData.suggestion)) : undefined, // Save suggestion
                  }
                ],
              },
            },
          });
          currentChatSectionId = newChatSection.id;
        } else {
          // Update existing chat section with new messages and topics
          await prisma.chatSection.update({
            where: { id: currentChatSectionId },
            data: {
              grammarRuleCount: rulesToSelect, // Update the grammar rule count
              // First, disconnect all existing topics for this chat section
              chatSectionTopics: {
                deleteMany: {},
              },
              // Then, connect the new set of selected topics
              messages: {
                create: [
                  {
                    role: 'user',
                    content: input,
                    correction: correctionData ? JSON.parse(JSON.stringify(correctionData)) : undefined, // Save correction to user message
                  },
                  {
                    role: 'assistant',
                    content: vocabularyData.message,
                    suggestion: vocabularyData.suggestion ? JSON.parse(JSON.stringify(vocabularyData.suggestion)) : undefined,
                  }
                ],
              },
            },
          });

          // Re-create chatSectionTopics after deletion
          if (selectedTopics.length > 0) {
            // First, get the CustomChatSetting IDs for the selected topic values
            const customChatSettings = await prisma.customChatSetting.findMany({
              where: {
                value: { in: selectedTopics }
              },
              select: { id: true, value: true }
            });

            await prisma.chatSectionTopic.createMany({
              data: customChatSettings.map(setting => ({
                chatSectionId: currentChatSectionId!,
                customChatSettingId: setting.id,
              })),
            });
          }
        }

        // Return the parsed response as JSON, including the chatSectionId

        return c.json({
          vocabularyResponse: validatedVocabulary,
          correctionResponse: validatedCorrection,
          chatSectionId: currentChatSectionId,
        });
      } catch (error: any) {
        console.error(`Attempt ${attempts} failed:`, error);
      }
    }

    if (!success) {
      console.error("All attempts to fetch and parse AI response failed.");
      // Return a default, well-structured response to prevent frontend errors
      return c.json({
        vocabularyResponse: {
          suggestion: {
            grammar_rule: "N/A",
            next_response_ideas: [],
            feedback: "",
          },
          message: "Sorry, I was unable to generate a response. Please try again.",
        },
        correctionResponse: {
          possibilties: [],
          correctedSentence: null,
        },
        chatSectionId: chatSectionId,
      });
    }

    // This part should ideally not be reached if success is handled inside the loop,
    // but as a fallback, we return an error.
    return c.json({ error: "An unexpected error occurred." }, 500);
  })
  .post("/multifunc", async (c) => {
    const openai = new ChatOpenAI({
      model: "gpt-4o-mini",
    }); /* .withStructuredOutput(schemaThemes) */

    const togetherai = new ChatTogetherAI({
      model: "deepseek-ai/DeepSeek-V3",
      temperature: 0,
    }); /* .withStructuredOutput(schemaThemes) */

    const litellmDeepSeek = new ChatOpenAI({
      model: "deepseek/deepseek-chat-v3-0324:free",
      configuration: {
        basePath: "https://litellm.tadzlab.xyz",
      },
      apiKey: process.env.LITELLM_API_KEY,
    });

    const mistralai = new ChatMistralAI({
      model: "mistral-small-latest",
    }); /* .withStructuredOutput(schemaThemes) */

    let success = false;
    let attempts = 0;

    while (!success && attempts < 5) {
      try {
        attempts += 1;
        // Parse the JSON body
        const { input, instruction } = await c.req.json();
        console.log("Received input:", input);

        const AgentState = Annotation.Root({
          messages: Annotation<BaseMessage[]>({
            reducer: (x, y) => x.concat(y),
            default: () => [new SystemMessage(instruction)],
          }),
        });

        const preAgent = new Agent({
          model: litellmDeepSeek,
          State: AgentState,
          tools: [searchtool],
        });
        const agent = preAgent.create();

        // Create a new human message
        const humanMessage = new HumanMessage(input);

        // Invoke the agent with the human message
        const response = await agent.invoke(
          { messages: [humanMessage] },
          { configurable: { thread_id: "8" } }
        );

        // Extract the last message from the response
        const lastMessage = response.messages[response.messages.length - 1];

        // Parse the response using StringOutputParser
        const parsedResponse = await new StringOutputParser().invoke(
          lastMessage
        );

        console.log("Parsed response:", parsedResponse);

        // Return the parsed response as JSON
        return c.json({ text: parsedResponse });
      } catch (error: any) {
        console.error("Error in vocabulary route:", error);
        // Return a 500 error with a JSON error message
      }
    }

    return c.json({ error: "Failed to fetch vocabulary data." }, 500);
  });

app.post("/flexible-translate", async (c) => {
  // 1. Parse the JSON body: text & the array of target languages
  const { textToTranslate, targetLanguages } = await c.req.json<{
    textToTranslate: string;
    targetLanguages: string[];
  }>();

  console.log("Received text:", textToTranslate);
  console.log("Requested languages:", targetLanguages);

  // 2. Set up the model with structured output
  const translatorModel = new ChatTogetherAI({
    model: "deepseek-ai/DeepSeek-V3",
    temperature: 0,
  }).withStructuredOutput(flexibleTranslationSchema);

  const litellmDeepSeek = new ChatOpenAI({
    model: "deepseek/deepseek-chat-v3-0324:free",
    configuration: {
      basePath: "https://litellm.tadzlab.xyz",
    },
    apiKey: process.env.LITELLM_API_KEY,
  });

  // 3. Create a system prompt that requests XML output
  const translatorState = Annotation.Root({
    messages: Annotation<BaseMessage[]>({
      reducer: (x, y) => x.concat(y),
      default: () => [
        new SystemMessage(`
          You are a multi-language translator.
          
          1. Automatically detect the language of the user input: \`${textToTranslate}\`.
          2. Translate it into each language in this list: ${targetLanguages.join(
            ", "
          )}.
          3. For each target language, provide possible translations (synonyms or phrasing variants).
          4. For each variant, provide IPA (phonetic transcription) as accurately as you can.
          5. Return your response in XML format like this:
          
          <translations>
            <language name="LANGUAGE_NAME">
              <translation>
                <text>translated text</text>
                <ipa>phonetic transcription</ipa>
              </translation>
              <!-- Additional translations for this language -->
            </language>
            <!-- Additional languages -->
          </translations>
        `),
      ],
    }),
  });

  // 4. Create an Agent
  const translatorAgent = new Agent({
    model: litellmDeepSeek,
    State: translatorState,
    tools: [searchtool],
  }).create();

  let success = false;
  let attempts = 0;

  while (!success && attempts < 5) {
    try {
      attempts += 1;
      // 5. Send the text as a HumanMessage to the agent
      const humanMessage = new HumanMessage(textToTranslate);
      const response = await translatorAgent.invoke(
        { messages: [humanMessage] },
        { configurable: { thread_id: "flexible-translation-thread" } }
      );

      // 6. Extract the XML response
      const xmlResponse = await new StringOutputParser().invoke(
        response.messages[response.messages.length - 1]
      );

      console.log("XML response:", xmlResponse);

      // 7. Parse XML to JSON
      const parser = new XMLParser({
        ignoreAttributes: false,
        isArray: (name) => name === "translation",
      });

      // Ensure we have a valid XML by wrapping if needed
      const xmlWithRoot = xmlResponse.includes("<translations>")
        ? xmlResponse
        : `<translations>${xmlResponse}</translations>`;

      const jsonObj = parser.parse(xmlWithRoot);

      interface Translation {
        text: string;
        ipa: string;
      }

      interface Language {
        translation:
          | { text: string; ipa: string }
          | { text: string; ipa: string }[];
      }

      // 8. Transform to match the expected format
      const transformedResponse: {
        translations: Translation[];
      } = {
        translations: [],
      };

      // Handle the case where there might be one or multiple languages
      const languages = Array.isArray(jsonObj.translations.language)
        ? jsonObj.translations.language
        : [jsonObj.translations.language];

      // Flatten all translations into a single array
      languages.forEach((lang: Language) => {
        const translations = Array.isArray(lang.translation)
          ? lang.translation
          : [lang.translation];

        translations.forEach((t) => {
          transformedResponse.translations.push({
            text: t.text,
            ipa: t.ipa,
          });
        });
      });

      console.log("Transformed response:", transformedResponse);
      success = true;

      // 9. Return the transformed response as JSON
      return c.json(transformedResponse);
    } catch (error: any) {
      console.error("Error in flexible-translate route:", error);
    }
  }

  return c.json({ error: "Failed to translate text." }, 500);
});

export default app;
